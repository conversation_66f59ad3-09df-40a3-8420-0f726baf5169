import { Image } from './image.entity';

describe('Image Entity', () => {
  let image: Image;

  beforeEach(() => {
    image = new Image();
  });

  describe('Entity Structure', () => {
    it('should create an instance', () => {
      expect(image).toBeDefined();
      expect(image).toBeInstanceOf(Image);
    });

    it('should have all required properties', () => {
      // Arrange & Act
      image.id = 1;
      image.name = 'Test Image';
      image.fileName = 'test-image.jpg';
      image.fileType = 'image/jpeg';
      image.filePath = 'uploads/images/test-image.jpg';
      image.createdAt = new Date();
      image.updatedAt = new Date();

      // Assert
      expect(image.id).toBe(1);
      expect(image.name).toBe('Test Image');
      expect(image.fileName).toBe('test-image.jpg');
      expect(image.fileType).toBe('image/jpeg');
      expect(image.filePath).toBe('uploads/images/test-image.jpg');
      expect(image.createdAt).toBeInstanceOf(Date);
      expect(image.updatedAt).toBeInstanceOf(Date);
    });

    it('should allow undefined id for new entities', () => {
      // Arrange & Act
      image.name = 'Test Image';
      image.fileName = 'test-image.jpg';
      image.fileType = 'image/jpeg';
      image.filePath = 'uploads/images/test-image.jpg';

      // Assert
      expect(image.id).toBeUndefined();
      expect(image.name).toBe('Test Image');
    });
  });

  describe('Property Types', () => {
    it('should handle string properties correctly', () => {
      // Arrange & Act
      image.name = 'Test Image Name';
      image.fileName = 'test-file-name.jpg';
      image.fileType = 'image/jpeg';
      image.filePath = '/path/to/file.jpg';

      // Assert
      expect(typeof image.name).toBe('string');
      expect(typeof image.fileName).toBe('string');
      expect(typeof image.fileType).toBe('string');
      expect(typeof image.filePath).toBe('string');
    });

    it('should handle numeric id correctly', () => {
      // Arrange & Act
      image.id = 123;

      // Assert
      expect(typeof image.id).toBe('number');
      expect(image.id).toBe(123);
    });

    it('should handle Date properties correctly', () => {
      // Arrange
      const now = new Date();
      
      // Act
      image.createdAt = now;
      image.updatedAt = now;

      // Assert
      expect(image.createdAt).toBeInstanceOf(Date);
      expect(image.updatedAt).toBeInstanceOf(Date);
      expect(image.createdAt).toBe(now);
      expect(image.updatedAt).toBe(now);
    });
  });

  describe('Property Constraints', () => {
    it('should handle long names within varchar(255) limit', () => {
      // Arrange
      const longName = 'A'.repeat(255);
      
      // Act
      image.name = longName;

      // Assert
      expect(image.name).toBe(longName);
      expect(image.name.length).toBe(255);
    });

    it('should handle file type within varchar(100) limit', () => {
      // Arrange
      const fileType = 'image/jpeg';
      
      // Act
      image.fileType = fileType;

      // Assert
      expect(image.fileType).toBe(fileType);
      expect(image.fileType.length).toBeLessThanOrEqual(100);
    });

    it('should handle file name within varchar(255) limit', () => {
      // Arrange
      const fileName = 'very-long-file-name-' + 'x'.repeat(200) + '.jpg';
      
      // Act
      image.fileName = fileName;

      // Assert
      expect(image.fileName).toBe(fileName);
      expect(image.fileName.length).toBeLessThanOrEqual(255);
    });

    it('should handle file path within varchar(255) limit', () => {
      // Arrange
      const filePath = 'uploads/images/' + 'long-path-' + 'x'.repeat(200) + '.jpg';
      
      // Act
      image.filePath = filePath;

      // Assert
      expect(image.filePath).toBe(filePath);
      expect(image.filePath.length).toBeLessThanOrEqual(255);
    });
  });

  describe('Common File Types', () => {
    const commonFileTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      'image/bmp',
      'image/tiff'
    ];

    commonFileTypes.forEach(fileType => {
      it(`should handle ${fileType} file type`, () => {
        // Act
        image.fileType = fileType;

        // Assert
        expect(image.fileType).toBe(fileType);
      });
    });
  });

  describe('Common File Extensions', () => {
    const commonExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.webp',
      '.svg',
      '.bmp',
      '.tiff'
    ];

    commonExtensions.forEach(extension => {
      it(`should handle files with ${extension} extension`, () => {
        // Arrange
        const fileName = `test-image${extension}`;
        
        // Act
        image.fileName = fileName;

        // Assert
        expect(image.fileName).toBe(fileName);
        expect(image.fileName.endsWith(extension)).toBe(true);
      });
    });
  });

  describe('Timestamp Behavior', () => {
    it('should handle creation and update timestamps', () => {
      // Arrange
      const createdAt = new Date('2024-01-01T00:00:00.000Z');
      const updatedAt = new Date('2024-01-02T00:00:00.000Z');
      
      // Act
      image.createdAt = createdAt;
      image.updatedAt = updatedAt;

      // Assert
      expect(image.createdAt).toBe(createdAt);
      expect(image.updatedAt).toBe(updatedAt);
      expect(image.updatedAt.getTime()).toBeGreaterThan(image.createdAt.getTime());
    });

    it('should handle same creation and update timestamps', () => {
      // Arrange
      const timestamp = new Date();
      
      // Act
      image.createdAt = timestamp;
      image.updatedAt = timestamp;

      // Assert
      expect(image.createdAt).toBe(timestamp);
      expect(image.updatedAt).toBe(timestamp);
      expect(image.createdAt.getTime()).toBe(image.updatedAt.getTime());
    });
  });

  describe('Entity Serialization', () => {
    it('should serialize to JSON correctly', () => {
      // Arrange
      image.id = 1;
      image.name = 'Test Image';
      image.fileName = 'test.jpg';
      image.fileType = 'image/jpeg';
      image.filePath = 'uploads/images/test.jpg';
      image.createdAt = new Date('2024-01-01T00:00:00.000Z');
      image.updatedAt = new Date('2024-01-01T00:00:00.000Z');

      // Act
      const json = JSON.stringify(image);
      const parsed = JSON.parse(json);

      // Assert
      expect(parsed.id).toBe(1);
      expect(parsed.name).toBe('Test Image');
      expect(parsed.fileName).toBe('test.jpg');
      expect(parsed.fileType).toBe('image/jpeg');
      expect(parsed.filePath).toBe('uploads/images/test.jpg');
      expect(parsed.createdAt).toBe('2024-01-01T00:00:00.000Z');
      expect(parsed.updatedAt).toBe('2024-01-01T00:00:00.000Z');
    });

    it('should handle partial data serialization', () => {
      // Arrange
      image.name = 'Partial Image';
      image.fileName = 'partial.jpg';

      // Act
      const json = JSON.stringify(image);
      const parsed = JSON.parse(json);

      // Assert
      expect(parsed.name).toBe('Partial Image');
      expect(parsed.fileName).toBe('partial.jpg');
      expect(parsed.id).toBeUndefined();
      expect(parsed.fileType).toBeUndefined();
    });
  });
});
