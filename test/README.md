# Tests Documentation

Ce document décrit la suite de tests complète pour le projet psite-back.

## Structure des Tests

```
test/
├── utils/                  # Utilitaires de test
│   ├── test-helpers.ts    # Helpers pour les tests unitaires
│   └── mock-data.ts       # Données mockées
├── setup.ts               # Configuration Jest pour tests unitaires
├── setup-e2e.ts          # Configuration Jest pour tests e2e
├── global-setup.ts        # Configuration globale Jest
├── global-teardown.ts     # Nettoyage global Jest
├── app.e2e-spec.ts       # Tests e2e de l'application
├── images.e2e-spec.ts    # Tests e2e du module images
└── README.md             # Cette documentation

src/
├── **/*.spec.ts          # Tests unitaires à côté du code source
├── app.controller.spec.ts
├── app.service.spec.ts
├── images/
│   ├── images.controller.spec.ts
│   ├── images.service.spec.ts
│   ├── entities/image.entity.spec.ts
│   ├── dto/create-image.dto.spec.ts
│   └── fixtures/
│       ├── image-fixtures.spec.ts
│       └── load-fixtures.command.spec.ts
```

## Types de Tests

### 1. Tests Unitaires (`*.spec.ts`)

Tests isolés pour chaque composant :

- **Services** : Logique métier, interactions avec la base de données
- **Contrôleurs** : Endpoints HTTP, validation des entrées
- **Entités** : Structure des données, validation
- **DTOs** : Objets de transfert de données
- **Fixtures** : Chargement des données de test
- **Commandes CLI** : Scripts de ligne de commande

### 2. Tests d'Intégration (E2E)

Tests complets de l'application :

- **API Endpoints** : Tests HTTP complets
- **Base de données** : Intégration avec TypeORM
- **Upload de fichiers** : Tests avec Multer
- **Sessions** : Gestion des sessions utilisateur

## Commandes de Test

### Tests Rapides
```bash
# Tests unitaires uniquement
yarn test

# Tests en mode watch
yarn test:watch

# Tests rapides avec script
./scripts/test-quick.sh
```

### Tests Complets
```bash
# Tous les tests avec couverture
yarn test:cov

# Tests e2e
yarn test:e2e

# Suite complète avec script
./scripts/test-all.sh
```

### Tests Spécifiques
```bash
# Test d'un fichier spécifique
yarn test images.service.spec.ts

# Test d'un pattern
yarn test --testNamePattern="should create"

# Tests avec verbose
yarn test --verbose
```

## Configuration Jest

### Configuration Principale (`jest.config.js`)
- Environnement Node.js
- Transformation TypeScript avec ts-jest
- Couverture de code avec seuils
- Configuration multi-projets (unit/e2e)

### Seuils de Couverture
- **Branches** : 80%
- **Fonctions** : 80%
- **Lignes** : 80%
- **Statements** : 80%

## Utilitaires de Test

### `test-helpers.ts`
- `createMockRepository()` : Mock TypeORM repository
- `createMockFile()` : Mock Express.Multer.File
- `createMockRequest()` : Mock Express Request avec session
- `createMockResponse()` : Mock Express Response
- `createTestingModule()` : Helper pour modules de test

### `mock-data.ts`
- `createMockImage()` : Données d'image de test
- `createMockImages()` : Plusieurs images de test
- `createMockCreateImageDto()` : DTO de création d'image
- Constantes de test (erreurs, chemins, tailles)

## Mocking Strategy

### Modules Externes
```typescript
// File system
jest.mock('fs')
jest.mock('path')

// HTTP
jest.mock('https')
jest.mock('http')
```

### Services NestJS
```typescript
const mockService = {
  method: jest.fn(),
};

// Dans le module de test
providers: [
  {
    provide: ServiceClass,
    useValue: mockService,
  },
]
```

### TypeORM Repository
```typescript
const mockRepository = {
  find: jest.fn(),
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  delete: jest.fn(),
};
```

## Patterns de Test

### Test Unitaire Standard
```typescript
describe('ServiceName', () => {
  let service: ServiceName;
  let repository: jest.Mocked<Repository<Entity>>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [ServiceName, mockRepository],
    }).compile();

    service = module.get<ServiceName>(ServiceName);
    repository = module.get(getRepositoryToken(Entity));
  });

  describe('methodName', () => {
    it('should do something', async () => {
      // Arrange
      repository.find.mockResolvedValue([]);

      // Act
      const result = await service.methodName();

      // Assert
      expect(result).toEqual([]);
      expect(repository.find).toHaveBeenCalled();
    });
  });
});
```

### Test E2E Standard
```typescript
describe('Endpoint (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/endpoint (GET)', () => {
    return request(app.getHttpServer())
      .get('/endpoint')
      .expect(200)
      .expect(expectedResponse);
  });
});
```

## Bonnes Pratiques

### 1. Structure AAA (Arrange-Act-Assert)
```typescript
it('should do something', () => {
  // Arrange - Préparer les données
  const input = 'test';
  
  // Act - Exécuter l'action
  const result = service.method(input);
  
  // Assert - Vérifier le résultat
  expect(result).toBe('expected');
});
```

### 2. Noms de Tests Descriptifs
```typescript
// ✅ Bon
it('should return 404 when image not found')

// ❌ Mauvais
it('should handle error')
```

### 3. Tests Isolés
- Chaque test doit être indépendant
- Utiliser `beforeEach` pour la configuration
- Nettoyer les mocks avec `afterEach`

### 4. Couverture Complète
- Tester les cas normaux
- Tester les cas d'erreur
- Tester les cas limites

## Debugging des Tests

### Mode Debug
```bash
# Debug avec Node.js
yarn test:debug

# Debug avec VS Code
# Ajouter un breakpoint et utiliser la configuration de debug
```

### Logs de Test
```typescript
// Activer les logs en mode test
console.log = jest.fn(); // Pour supprimer les logs
// ou
console.log('Debug info'); // Pour garder certains logs
```

### Tests Isolés
```bash
# Exécuter un seul test
yarn test --testNamePattern="specific test name"

# Exécuter un seul fichier
yarn test path/to/test.spec.ts
```

## Maintenance des Tests

### Mise à Jour des Mocks
- Vérifier régulièrement que les mocks correspondent aux vraies APIs
- Mettre à jour les données de test quand le schéma change

### Performance
- Éviter les tests trop lents
- Utiliser des mocks pour les opérations coûteuses
- Paralléliser quand possible

### CI/CD Integration
Les tests sont conçus pour s'intégrer facilement dans les pipelines CI/CD :
- Génération de rapports JUnit
- Rapports de couverture LCOV
- Codes de sortie appropriés

## Troubleshooting

### Problèmes Courants

1. **Tests qui échouent de manière intermittente**
   - Vérifier les opérations asynchrones
   - Augmenter les timeouts si nécessaire

2. **Mocks qui ne fonctionnent pas**
   - Vérifier l'ordre des imports
   - S'assurer que les mocks sont définis avant les imports

3. **Problèmes de base de données**
   - Utiliser une base de données en mémoire pour les tests
   - Nettoyer les données entre les tests

4. **Problèmes de fichiers**
   - Nettoyer les fichiers temporaires
   - Utiliser des chemins relatifs au projet
