#!/bin/bash

# Script pour exécuter les tests rapidement (sans couverture complète)
# Usage: ./scripts/test-quick.sh

set -e

echo "⚡ Running quick test suite for psite-back..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Run unit tests only (fast)
print_status "Running unit tests..."
yarn test --passWithNoTests

print_success "⚡ Quick tests completed!"
