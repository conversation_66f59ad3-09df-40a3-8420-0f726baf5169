import { Test, TestingModule } from '@nestjs/testing';
import { AppService } from './app.service';

describe('AppService', () => {
  let service: AppService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AppService],
    }).compile();

    service = module.get<AppService>(AppService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Initialization', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should be an instance of AppService', () => {
      expect(service).toBeInstanceOf(AppService);
    });

    it('should have getHello method', () => {
      expect(service.getHello).toBeDefined();
      expect(typeof service.getHello).toBe('function');
    });
  });

  describe('getHello', () => {
    it('should return "Hello World!"', () => {
      // Act
      const result = service.getHello();

      // Assert
      expect(result).toBe('Hello World!');
    });

    it('should return a string', () => {
      // Act
      const result = service.getHello();

      // Assert
      expect(typeof result).toBe('string');
    });

    it('should return the same value on multiple calls', () => {
      // Act
      const result1 = service.getHello();
      const result2 = service.getHello();
      const result3 = service.getHello();

      // Assert
      expect(result1).toBe(result2);
      expect(result2).toBe(result3);
      expect(result1).toBe('Hello World!');
    });

    it('should not be null or undefined', () => {
      // Act
      const result = service.getHello();

      // Assert
      expect(result).not.toBeNull();
      expect(result).not.toBeUndefined();
    });

    it('should not be an empty string', () => {
      // Act
      const result = service.getHello();

      // Assert
      expect(result).not.toBe('');
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe('Service Behavior', () => {
    it('should be stateless', () => {
      // This test ensures the service doesn't maintain state between calls
      const result1 = service.getHello();
      const result2 = service.getHello();
      
      expect(result1).toBe(result2);
    });

    it('should be synchronous', () => {
      // Act
      const start = Date.now();
      const result = service.getHello();
      const end = Date.now();

      // Assert
      expect(result).toBe('Hello World!');
      expect(end - start).toBeLessThan(10); // Should be very fast
    });
  });

  describe('Edge Cases', () => {
    it('should handle multiple service instances', async () => {
      // Arrange
      const module2: TestingModule = await Test.createTestingModule({
        providers: [AppService],
      }).compile();
      const service2 = module2.get<AppService>(AppService);

      // Act
      const result1 = service.getHello();
      const result2 = service2.getHello();

      // Assert
      expect(result1).toBe(result2);
      expect(result1).toBe('Hello World!');
    });

    it('should work after service recreation', async () => {
      // Arrange
      const originalResult = service.getHello();
      
      // Recreate service
      const newModule: TestingModule = await Test.createTestingModule({
        providers: [AppService],
      }).compile();
      const newService = newModule.get<AppService>(AppService);

      // Act
      const newResult = newService.getHello();

      // Assert
      expect(newResult).toBe(originalResult);
      expect(newResult).toBe('Hello World!');
    });
  });
});
