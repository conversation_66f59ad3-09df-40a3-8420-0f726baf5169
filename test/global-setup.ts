/**
 * Global setup for Jest tests
 */

import * as fs from 'fs';
import * as path from 'path';

export default async function globalSetup() {
  console.log('🚀 Setting up Jest test environment...');

  // Create necessary test directories
  const testDirs = [
    path.join(__dirname, 'fixtures'),
    path.join(__dirname, '../coverage'),
    path.join(__dirname, '../test-uploads'),
    path.join(__dirname, '../test-uploads/images'),
  ];

  testDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 Created test directory: ${dir}`);
    }
  });

  // Set global test environment variables
  process.env.NODE_ENV = 'test';
  process.env.TZ = 'UTC';
  
  // Database configuration for tests
  process.env.DATABASE_HOST = process.env.TEST_DATABASE_HOST || 'localhost';
  process.env.DATABASE_PORT = process.env.TEST_DATABASE_PORT || '5432';
  process.env.DATABASE_USER = process.env.TEST_DATABASE_USER || 'test_user';
  process.env.DATABASE_PASSWORD = process.env.TEST_DATABASE_PASSWORD || 'test_password';
  process.env.DATABASE_NAME = process.env.TEST_DATABASE_NAME || 'test_psite_db';
  
  // Session configuration for tests
  process.env.SESSION_SECRET = 'test-session-secret-key';
  
  // Server configuration for tests
  process.env.PORT = process.env.TEST_PORT || '3001';

  // Disable external services in tests
  process.env.DISABLE_EXTERNAL_SERVICES = 'true';

  console.log('✅ Jest test environment setup completed');
}
