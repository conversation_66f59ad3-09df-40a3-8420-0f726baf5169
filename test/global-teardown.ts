/**
 * Global teardown for Jest tests
 */

import * as fs from 'fs';
import * as path from 'path';

export default async function globalTeardown() {
  console.log('🧹 Cleaning up Jest test environment...');

  // Clean up test directories
  const testDirs = [
    path.join(__dirname, 'fixtures'),
    path.join(__dirname, '../test-uploads'),
  ];

  testDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      try {
        fs.rmSync(dir, { recursive: true, force: true });
        console.log(`🗑️  Removed test directory: ${dir}`);
      } catch (error) {
        console.warn(`⚠️  Failed to remove test directory ${dir}:`, error);
      }
    }
  });

  // Clean up any temporary test files
  const tempFiles = [
    path.join(__dirname, '../temp-test-file.txt'),
    path.join(__dirname, '../test-image.jpg'),
    path.join(__dirname, '../test-image.png'),
  ];

  tempFiles.forEach(file => {
    if (fs.existsSync(file)) {
      try {
        fs.unlinkSync(file);
        console.log(`🗑️  Removed temporary file: ${file}`);
      } catch (error) {
        console.warn(`⚠️  Failed to remove temporary file ${file}:`, error);
      }
    }
  });

  // Reset environment variables
  delete process.env.NODE_ENV;
  delete process.env.TZ;
  delete process.env.DATABASE_HOST;
  delete process.env.DATABASE_PORT;
  delete process.env.DATABASE_USER;
  delete process.env.DATABASE_PASSWORD;
  delete process.env.DATABASE_NAME;
  delete process.env.SESSION_SECRET;
  delete process.env.PORT;
  delete process.env.DISABLE_EXTERNAL_SERVICES;

  console.log('✅ Jest test environment cleanup completed');
}
