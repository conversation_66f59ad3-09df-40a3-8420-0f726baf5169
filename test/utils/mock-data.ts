import { Image } from '../../src/images/entities/image.entity';
import { CreateImageDto } from '../../src/images/dto/create-image.dto';

/**
 * Données de test mockées
 */

/**
 * Crée une image de test
 */
export const createMockImage = (overrides: Partial<Image> = {}): Image => {
  const image = new Image();
  image.id = 1;
  image.name = 'Test Image';
  image.fileName = 'test-image-123.jpg';
  image.fileType = 'image/jpeg';
  image.filePath = 'uploads/images/test-image-123.jpg';
  image.createdAt = new Date('2024-01-01T00:00:00.000Z');
  image.updatedAt = new Date('2024-01-01T00:00:00.000Z');

  return Object.assign(image, overrides);
};

/**
 * Crée plusieurs images de test
 */
export const createMockImages = (count: number = 3): Image[] => {
  return Array.from({ length: count }, (_, index) =>
    createMockImage({
      id: index + 1,
      name: `Test Image ${index + 1}`,
      fileName: `test-image-${index + 1}-123.jpg`,
      filePath: `uploads/images/test-image-${index + 1}-123.jpg`,
    }),
  );
};

/**
 * Crée un DTO de création d'image de test
 */
export const createMockCreateImageDto = (
  overrides: Partial<CreateImageDto> = {},
): CreateImageDto => ({
  name: 'Test Image',
  ...overrides,
});
