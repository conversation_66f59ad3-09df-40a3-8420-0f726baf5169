/**
 * Jest setup file for unit tests
 */

// Set test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

beforeAll(() => {
  // Mock console.error to suppress expected error messages in tests
  console.error = jest.fn((message, ...args) => {
    // Only suppress specific expected errors, log others
    if (
      typeof message === 'string' &&
      (message.includes('Failed to create fixture image') ||
       message.includes('Database connection') ||
       message.includes('Test error'))
    ) {
      return;
    }
    originalConsoleError(message, ...args);
  });

  // Mock console.warn to suppress warnings
  console.warn = jest.fn((message, ...args) => {
    if (
      typeof message === 'string' &&
      (message.includes('Fichier déjà supprimé') ||
       message.includes('deprecated'))
    ) {
      return;
    }
    originalConsoleWarn(message, ...args);
  });

  // Mock console.log for fixture loading messages
  console.log = jest.fn((message, ...args) => {
    if (
      typeof message === 'string' &&
      (message.includes('Loading fixtures') ||
       message.includes('Fixtures loaded') ||
       message.includes('Created fixture image') ||
       message.includes('Skipping image fixtures'))
    ) {
      return;
    }
    originalConsoleLog(message, ...args);
  });
});

afterAll(() => {
  // Restore original console methods
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  console.log = originalConsoleLog;
});

// Global test utilities
global.testUtils = {
  // Helper to create mock dates
  createMockDate: (dateString?: string) => {
    return dateString ? new Date(dateString) : new Date('2024-01-01T00:00:00.000Z');
  },

  // Helper to wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to generate random test data
  generateRandomString: (length: number = 10) => {
    return Math.random().toString(36).substring(2, 2 + length);
  },

  // Helper to generate random numbers
  generateRandomNumber: (min: number = 1, max: number = 1000) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },
};

// Mock environment variables for tests
process.env.NODE_ENV = 'test';
process.env.DATABASE_HOST = 'localhost';
process.env.DATABASE_PORT = '5432';
process.env.DATABASE_USER = 'test';
process.env.DATABASE_PASSWORD = 'test';
process.env.DATABASE_NAME = 'test_db';
process.env.SESSION_SECRET = 'test-secret';

// Mock file system operations that might interfere with tests
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
  createWriteStream: jest.fn(),
  promises: {
    access: jest.fn(),
    unlink: jest.fn(),
  },
  constants: {
    F_OK: 0,
  },
}));

// Mock path operations
jest.mock('path', () => ({
  ...jest.requireActual('path'),
  join: jest.fn((...args) => args.join('/')),
  extname: jest.fn((filename) => {
    const parts = filename.split('.');
    return parts.length > 1 ? `.${parts[parts.length - 1]}` : '';
  }),
}));

// Mock HTTP modules for fixture tests
jest.mock('https', () => ({
  get: jest.fn(),
}));

jest.mock('http', () => ({
  get: jest.fn(),
}));

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Increase the default timeout for async operations
jest.setTimeout(30000);

// Custom matchers
expect.extend({
  toBeValidImage(received) {
    const pass = received &&
      typeof received.id === 'number' &&
      typeof received.name === 'string' &&
      typeof received.fileName === 'string' &&
      typeof received.fileType === 'string' &&
      typeof received.filePath === 'string' &&
      received.createdAt instanceof Date &&
      received.updatedAt instanceof Date;

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid image`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid image`,
        pass: false,
      };
    }
  },

  toBeValidCreateImageDto(received) {
    const pass = received &&
      (typeof received.name === 'string' || received.name === undefined);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid CreateImageDto`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid CreateImageDto`,
        pass: false,
      };
    }
  },
});

// Declare global types for TypeScript
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidImage(): R;
      toBeValidCreateImageDto(): R;
    }
  }

  var testUtils: {
    createMockDate: (dateString?: string) => Date;
    waitFor: (ms: number) => Promise<void>;
    generateRandomString: (length?: number) => string;
    generateRandomNumber: (min?: number, max?: number) => number;
  };
}
