import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { AppModule } from './../src/app.module';

describe('AppController (e2e)', () => {
  let app: INestApplication<App>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();

    // Apply the same configuration as in main.ts
    app.enableCors({
      origin: ['http://localhost:5173', 'http://localhost:4173'],
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      credentials: true,
    });

    app.setGlobalPrefix('api');
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Root Endpoint', () => {
    it('/api (GET) - should return Hello World!', () => {
      return request(app.getHttpServer())
        .get('/api')
        .expect(200)
        .expect('Hello World!');
    });

    it('/api (GET) - should return string content type', () => {
      return request(app.getHttpServer())
        .get('/api')
        .expect(200)
        .expect('Content-Type', /text\/html/);
    });

    it('/api (GET) - should be accessible multiple times', async () => {
      const requests = Array.from({ length: 5 }, () =>
        request(app.getHttpServer())
          .get('/api')
          .expect(200)
          .expect('Hello World!')
      );

      await Promise.all(requests);
    });
  });

  describe('CORS Configuration', () => {
    it('should include CORS headers', () => {
      return request(app.getHttpServer())
        .get('/api')
        .expect(200)
        .expect((res) => {
          // CORS headers should be present
          expect(res.headers).toHaveProperty('access-control-allow-origin');
        });
    });

    it('should handle OPTIONS preflight request', () => {
      return request(app.getHttpServer())
        .options('/api')
        .expect(204);
    });
  });

  describe('Global Prefix', () => {
    it('should not be accessible without api prefix', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect(404);
    });

    it('should be accessible with api prefix', () => {
      return request(app.getHttpServer())
        .get('/api')
        .expect(200);
    });
  });

  describe('HTTP Methods', () => {
    it('should support GET method', () => {
      return request(app.getHttpServer())
        .get('/api')
        .expect(200);
    });

    it('should return 404 for unsupported methods on root', () => {
      return request(app.getHttpServer())
        .post('/api')
        .expect(404);
    });

    it('should return 404 for PUT method on root', () => {
      return request(app.getHttpServer())
        .put('/api')
        .expect(404);
    });

    it('should return 404 for DELETE method on root', () => {
      return request(app.getHttpServer())
        .delete('/api')
        .expect(404);
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for non-existent routes', () => {
      return request(app.getHttpServer())
        .get('/api/non-existent-route')
        .expect(404);
    });

    it('should handle malformed requests', () => {
      return request(app.getHttpServer())
        .get('/api/../../../etc/passwd')
        .expect(404);
    });
  });

  describe('Application Health', () => {
    it('should respond quickly', async () => {
      const start = Date.now();

      await request(app.getHttpServer())
        .get('/api')
        .expect(200);

      const duration = Date.now() - start;
      expect(duration).toBeLessThan(1000); // Should respond within 1 second
    });

    it('should handle concurrent requests', async () => {
      const concurrentRequests = 10;
      const requests = Array.from({ length: concurrentRequests }, () =>
        request(app.getHttpServer())
          .get('/api')
          .expect(200)
          .expect('Hello World!')
      );

      const results = await Promise.all(requests);
      expect(results).toHaveLength(concurrentRequests);
    });
  });

  describe('Content Validation', () => {
    it('should return exact expected content', () => {
      return request(app.getHttpServer())
        .get('/api')
        .expect(200)
        .expect((res) => {
          expect(res.text).toBe('Hello World!');
          expect(res.text.length).toBe(12);
        });
    });

    it('should not return empty response', () => {
      return request(app.getHttpServer())
        .get('/api')
        .expect(200)
        .expect((res) => {
          expect(res.text).toBeTruthy();
          expect(res.text.trim()).not.toBe('');
        });
    });
  });
});
