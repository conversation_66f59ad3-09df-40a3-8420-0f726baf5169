/**
 * Jest setup file for e2e tests
 */

import * as fs from 'fs';
import * as path from 'path';

// Set test timeout for e2e tests (longer than unit tests)
jest.setTimeout(60000);

// Setup test directories
const setupTestDirectories = () => {
  const testDirs = [
    path.join(__dirname, 'fixtures'),
    path.join(__dirname, '../uploads/images'),
    path.join(__dirname, '../test-uploads/images'),
  ];

  testDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
};

// Cleanup test files
const cleanupTestFiles = () => {
  const testDirs = [
    path.join(__dirname, 'fixtures'),
    path.join(__dirname, '../test-uploads'),
  ];

  testDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      try {
        fs.rmSync(dir, { recursive: true, force: true });
      } catch (error) {
        console.warn(`Failed to cleanup test directory ${dir}:`, error);
      }
    }
  });
};

// Setup before all tests
beforeAll(async () => {
  setupTestDirectories();
  
  // Set environment variables for e2e tests
  process.env.NODE_ENV = 'test';
  process.env.DATABASE_HOST = 'localhost';
  process.env.DATABASE_PORT = '5432';
  process.env.DATABASE_USER = 'test_user';
  process.env.DATABASE_PASSWORD = 'test_password';
  process.env.DATABASE_NAME = 'test_psite_db';
  process.env.SESSION_SECRET = 'test-session-secret';
  process.env.PORT = '3001';
});

// Cleanup after all tests
afterAll(async () => {
  cleanupTestFiles();
  
  // Give some time for cleanup
  await new Promise(resolve => setTimeout(resolve, 1000));
});

// Setup before each test
beforeEach(() => {
  // Clean up any test files created during individual tests
  const testFixturesDir = path.join(__dirname, 'fixtures');
  if (fs.existsSync(testFixturesDir)) {
    const files = fs.readdirSync(testFixturesDir);
    files.forEach(file => {
      if (file.startsWith('test-image')) {
        try {
          fs.unlinkSync(path.join(testFixturesDir, file));
        } catch (error) {
          // Ignore cleanup errors
        }
      }
    });
  }
});

// Global utilities for e2e tests
global.e2eUtils = {
  // Create a test image file
  createTestImageFile: (filename: string, content: string = 'fake image data') => {
    const testDir = path.join(__dirname, 'fixtures');
    const filePath = path.join(testDir, filename);
    
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    fs.writeFileSync(filePath, Buffer.from(content));
    return filePath;
  },

  // Remove a test file
  removeTestFile: (filePath: string) => {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.warn(`Failed to remove test file ${filePath}:`, error);
    }
  },

  // Wait for server to be ready
  waitForServer: (ms: number = 1000) => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // Generate test image data
  generateImageBuffer: (size: number = 1024) => {
    return Buffer.alloc(size, 'test image data');
  },

  // Create test image with specific properties
  createTestImage: (options: {
    name?: string;
    type?: string;
    size?: number;
  } = {}) => {
    const {
      name = 'test-image.jpg',
      type = 'image/jpeg',
      size = 1024
    } = options;

    const buffer = Buffer.alloc(size, 'fake image data');
    const filePath = global.e2eUtils.createTestImageFile(name, buffer.toString());

    return {
      name,
      type,
      size,
      buffer,
      filePath,
      cleanup: () => global.e2eUtils.removeTestFile(filePath)
    };
  },
};

// Mock external services for e2e tests
jest.mock('https', () => ({
  get: jest.fn((url, callback) => {
    const mockResponse = {
      statusCode: 200,
      on: jest.fn(),
      pipe: jest.fn(),
    };
    
    const mockRequest = {
      on: jest.fn(),
      end: jest.fn(),
    };

    if (callback) {
      setTimeout(() => callback(mockResponse), 100);
    }
    
    return mockRequest;
  }),
}));

jest.mock('http', () => ({
  get: jest.fn((url, callback) => {
    const mockResponse = {
      statusCode: 200,
      on: jest.fn(),
      pipe: jest.fn(),
    };
    
    const mockRequest = {
      on: jest.fn(),
      end: jest.fn(),
    };

    if (callback) {
      setTimeout(() => callback(mockResponse), 100);
    }
    
    return mockRequest;
  }),
}));

// Handle unhandled promise rejections in e2e tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection in e2e test at:', promise, 'reason:', reason);
});

// Handle uncaught exceptions in e2e tests
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception in e2e test:', error);
});

// Custom matchers for e2e tests
expect.extend({
  toHaveValidImageResponse(received) {
    const pass = received &&
      received.body &&
      typeof received.body.id === 'number' &&
      typeof received.body.fileName === 'string' &&
      typeof received.body.fileType === 'string' &&
      typeof received.body.filePath === 'string' &&
      typeof received.body.createdAt === 'string' &&
      typeof received.body.updatedAt === 'string';

    if (pass) {
      return {
        message: () => `expected response not to have valid image structure`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected response to have valid image structure`,
        pass: false,
      };
    }
  },

  toHaveValidErrorResponse(received, expectedMessage?: string) {
    const pass = received &&
      received.body &&
      typeof received.body.message === 'string' &&
      (expectedMessage ? received.body.message === expectedMessage : true);

    if (pass) {
      return {
        message: () => `expected response not to have valid error structure`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected response to have valid error structure${expectedMessage ? ` with message "${expectedMessage}"` : ''}`,
        pass: false,
      };
    }
  },
});

// Declare global types for TypeScript
declare global {
  var e2eUtils: {
    createTestImageFile: (filename: string, content?: string) => string;
    removeTestFile: (filePath: string) => void;
    waitForServer: (ms?: number) => Promise<void>;
    generateImageBuffer: (size?: number) => Buffer;
    createTestImage: (options?: {
      name?: string;
      type?: string;
      size?: number;
    }) => {
      name: string;
      type: string;
      size: number;
      buffer: Buffer;
      filePath: string;
      cleanup: () => void;
    };
  };

  namespace jest {
    interface Matchers<R> {
      toHaveValidImageResponse(): R;
      toHaveValidErrorResponse(expectedMessage?: string): R;
    }
  }
}
