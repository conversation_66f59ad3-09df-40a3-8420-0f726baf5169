import { Test, TestingModule } from '@nestjs/testing';
import { LoadFixturesCommand } from './load-fixtures.command';
import { ImageFixtures } from './image-fixtures';

describe('LoadFixturesCommand', () => {
  let command: LoadFixturesCommand;
  let imageFixtures: jest.Mocked<ImageFixtures>;

  const mockImageFixtures = {
    load: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LoadFixturesCommand,
        {
          provide: ImageFixtures,
          useValue: mockImageFixtures,
        },
      ],
    }).compile();

    command = module.get<LoadFixturesCommand>(LoadFixturesCommand);
    imageFixtures = module.get<ImageFixtures>(ImageFixtures) as jest.Mocked<ImageFixtures>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('run', () => {
    it('should load fixtures successfully', async () => {
      // Arrange
      imageFixtures.load.mockResolvedValue(undefined);
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      await command.run();

      // Assert
      expect(consoleLogSpy).toHaveBeenCalledWith('Loading fixtures...');
      expect(imageFixtures.load).toHaveBeenCalled();
      expect(consoleLogSpy).toHaveBeenCalledWith('Fixtures loaded successfully');
      
      consoleLogSpy.mockRestore();
    });

    it('should handle fixtures loading errors', async () => {
      // Arrange
      const error = new Error('Database connection failed');
      imageFixtures.load.mockRejectedValue(error);
      
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Act
      await command.run();

      // Assert
      expect(consoleLogSpy).toHaveBeenCalledWith('Loading fixtures...');
      expect(imageFixtures.load).toHaveBeenCalled();
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to load fixtures:', error);
      expect(consoleLogSpy).not.toHaveBeenCalledWith('Fixtures loaded successfully');
      
      consoleLogSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it('should handle network errors during fixtures loading', async () => {
      // Arrange
      const networkError = new Error('Network timeout');
      imageFixtures.load.mockRejectedValue(networkError);
      
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Act
      await command.run();

      // Assert
      expect(consoleLogSpy).toHaveBeenCalledWith('Loading fixtures...');
      expect(imageFixtures.load).toHaveBeenCalled();
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to load fixtures:', networkError);
      
      consoleLogSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it('should handle file system errors during fixtures loading', async () => {
      // Arrange
      const fsError = new Error('EACCES: permission denied');
      imageFixtures.load.mockRejectedValue(fsError);
      
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Act
      await command.run();

      // Assert
      expect(consoleLogSpy).toHaveBeenCalledWith('Loading fixtures...');
      expect(imageFixtures.load).toHaveBeenCalled();
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to load fixtures:', fsError);
      
      consoleLogSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it('should not throw errors even when fixtures loading fails', async () => {
      // Arrange
      const error = new Error('Critical error');
      imageFixtures.load.mockRejectedValue(error);
      
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Act & Assert
      await expect(command.run()).resolves.toBeUndefined();
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to load fixtures:', error);
      
      consoleLogSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it('should call imageFixtures.load exactly once', async () => {
      // Arrange
      imageFixtures.load.mockResolvedValue(undefined);
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      await command.run();

      // Assert
      expect(imageFixtures.load).toHaveBeenCalledTimes(1);
      expect(imageFixtures.load).toHaveBeenCalledWith();
      
      consoleLogSpy.mockRestore();
    });

    it('should log appropriate messages in correct order', async () => {
      // Arrange
      imageFixtures.load.mockResolvedValue(undefined);
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      await command.run();

      // Assert
      expect(consoleLogSpy).toHaveBeenNthCalledWith(1, 'Loading fixtures...');
      expect(consoleLogSpy).toHaveBeenNthCalledWith(2, 'Fixtures loaded successfully');
      expect(consoleLogSpy).toHaveBeenCalledTimes(2);
      
      consoleLogSpy.mockRestore();
    });
  });

  describe('Command Configuration', () => {
    it('should be properly configured as a command', () => {
      // This test ensures the command is properly decorated
      expect(command).toBeInstanceOf(LoadFixturesCommand);
      expect(command.run).toBeDefined();
      expect(typeof command.run).toBe('function');
    });

    it('should have access to ImageFixtures service', () => {
      expect(imageFixtures).toBeDefined();
      expect(imageFixtures.load).toBeDefined();
      expect(typeof imageFixtures.load).toBe('function');
    });
  });

  describe('Error Handling Scenarios', () => {
    it('should handle undefined error', async () => {
      // Arrange
      imageFixtures.load.mockRejectedValue(undefined);
      
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Act
      await command.run();

      // Assert
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to load fixtures:', undefined);
      
      consoleLogSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it('should handle string error', async () => {
      // Arrange
      imageFixtures.load.mockRejectedValue('String error message');
      
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Act
      await command.run();

      // Assert
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to load fixtures:', 'String error message');
      
      consoleLogSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it('should handle object error', async () => {
      // Arrange
      const objectError = { message: 'Object error', code: 500 };
      imageFixtures.load.mockRejectedValue(objectError);
      
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Act
      await command.run();

      // Assert
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to load fixtures:', objectError);
      
      consoleLogSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });
  });

  describe('Integration Scenarios', () => {
    it('should work with slow fixtures loading', async () => {
      // Arrange
      const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
      imageFixtures.load.mockImplementation(async () => {
        await delay(100); // Simulate slow loading
      });
      
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      const startTime = Date.now();
      await command.run();
      const endTime = Date.now();

      // Assert
      expect(endTime - startTime).toBeGreaterThanOrEqual(100);
      expect(consoleLogSpy).toHaveBeenCalledWith('Fixtures loaded successfully');
      
      consoleLogSpy.mockRestore();
    });

    it('should work with fixtures that load immediately', async () => {
      // Arrange
      imageFixtures.load.mockResolvedValue(undefined);
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      const startTime = Date.now();
      await command.run();
      const endTime = Date.now();

      // Assert
      expect(endTime - startTime).toBeLessThan(50); // Should be very fast
      expect(consoleLogSpy).toHaveBeenCalledWith('Fixtures loaded successfully');
      
      consoleLogSpy.mockRestore();
    });
  });
});
