#!/bin/bash

# Script pour exécuter tous les tests Jest avec couverture complète
# Usage: ./scripts/test-all.sh

set -e

echo "🧪 Starting comprehensive test suite for psite-back..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if yarn is available
if ! command -v yarn &> /dev/null; then
    print_error "Yarn is not installed. Please install yarn first."
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Clean previous test artifacts
print_status "Cleaning previous test artifacts..."
rm -rf coverage/
rm -rf test-uploads/
rm -rf dist/
mkdir -p coverage

# Install dependencies if needed
print_status "Checking dependencies..."
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    yarn install
else
    print_status "Dependencies already installed"
fi

# Lint the code first
print_status "Running ESLint..."
if yarn lint; then
    print_success "Linting passed"
else
    print_warning "Linting failed, but continuing with tests..."
fi

# Build the project
print_status "Building the project..."
if yarn build; then
    print_success "Build successful"
else
    print_error "Build failed"
    exit 1
fi

# Run unit tests
print_status "Running unit tests..."
if yarn test --coverage --verbose --passWithNoTests; then
    print_success "Unit tests passed"
else
    print_error "Unit tests failed"
    exit 1
fi

# Run e2e tests
print_status "Running e2e tests..."
if yarn test:e2e --verbose --passWithNoTests; then
    print_success "E2E tests passed"
else
    print_error "E2E tests failed"
    exit 1
fi

# Generate comprehensive coverage report
print_status "Generating comprehensive coverage report..."
if yarn test:cov --verbose --passWithNoTests; then
    print_success "Coverage report generated"
else
    print_warning "Coverage report generation had issues, but tests passed"
fi

# Check coverage thresholds
print_status "Checking coverage thresholds..."
if [ -f "coverage/lcov-report/index.html" ]; then
    print_success "Coverage report available at: coverage/lcov-report/index.html"
fi

# Display coverage summary
if [ -f "coverage/coverage-summary.json" ]; then
    print_status "Coverage Summary:"
    if command -v jq &> /dev/null; then
        jq '.total' coverage/coverage-summary.json
    else
        print_warning "Install jq to see detailed coverage summary"
    fi
fi

# Run additional quality checks
print_status "Running additional quality checks..."

# Check for TODO/FIXME comments
print_status "Checking for TODO/FIXME comments..."
TODO_COUNT=$(grep -r "TODO\|FIXME" src/ --include="*.ts" | wc -l || echo "0")
if [ "$TODO_COUNT" -gt 0 ]; then
    print_warning "Found $TODO_COUNT TODO/FIXME comments in source code"
    grep -r "TODO\|FIXME" src/ --include="*.ts" || true
else
    print_success "No TODO/FIXME comments found"
fi

# Check for console.log statements (excluding test files)
print_status "Checking for console.log statements..."
CONSOLE_COUNT=$(grep -r "console\." src/ --include="*.ts" --exclude="*.spec.ts" | wc -l || echo "0")
if [ "$CONSOLE_COUNT" -gt 0 ]; then
    print_warning "Found $CONSOLE_COUNT console statements in source code"
    grep -r "console\." src/ --include="*.ts" --exclude="*.spec.ts" || true
else
    print_success "No console statements found in source code"
fi

# Check test file coverage
print_status "Checking test file coverage..."
SRC_FILES=$(find src/ -name "*.ts" -not -name "*.spec.ts" -not -name "*.e2e-spec.ts" | wc -l)
TEST_FILES=$(find src/ -name "*.spec.ts" | wc -l)
E2E_FILES=$(find test/ -name "*.e2e-spec.ts" | wc -l)

print_status "Source files: $SRC_FILES"
print_status "Unit test files: $TEST_FILES"
print_status "E2E test files: $E2E_FILES"

if [ "$TEST_FILES" -lt "$SRC_FILES" ]; then
    print_warning "Not all source files have corresponding test files"
else
    print_success "Good test file coverage"
fi

# Final summary
print_status "Test Summary:"
echo "=================="
print_success "✅ Unit tests: PASSED"
print_success "✅ E2E tests: PASSED"
print_success "✅ Build: PASSED"
print_success "✅ Coverage report: GENERATED"
echo "=================="

print_success "🎉 All tests completed successfully!"
print_status "Coverage report: coverage/lcov-report/index.html"
print_status "JUnit report: coverage/junit.xml"

# Open coverage report if on macOS
if [[ "$OSTYPE" == "darwin"* ]] && [ -f "coverage/lcov-report/index.html" ]; then
    read -p "Open coverage report in browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open coverage/lcov-report/index.html
    fi
fi

exit 0
