import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';

import { ImagesController } from '../src/images/images.controller';
import { ImagesService } from '../src/images/images.service';

// Mock ImagesService
const mockImagesService = {
  findAll: jest.fn().mockResolvedValue([]),
  findOne: jest.fn().mockResolvedValue(null),
  create: jest.fn().mockResolvedValue({ id: 1, name: 'Test Image' }),
  delete: jest.fn().mockResolvedValue(undefined),
  getRandom: jest.fn().mockResolvedValue([]),
};

describe('Images (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [ImagesController],
      providers: [
        {
          provide: ImagesService,
          useValue: mockImagesService,
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.setGlobalPrefix('api');
    await app.init();
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('/api/images (GET)', () => {
    it('should return empty array initially', () => {
      return request(app.getHttpServer())
        .get('/api/images')
        .expect(200)
        .expect([]);
    });
  });

  describe('/api/images/:id (GET)', () => {
    it('should return 404 for non-existent image', () => {
      mockImagesService.findOne.mockResolvedValue(null);

      return request(app.getHttpServer())
        .get('/api/images/999999')
        .expect(404);
    });
  });

  describe('Basic API Tests', () => {
    it('should handle basic GET requests', () => {
      return request(app.getHttpServer())
        .get('/api/images')
        .expect(200);
    });
  });

  describe('Error Handling', () => {
    it('should handle unsupported HTTP methods', () => {
      return request(app.getHttpServer())
        .patch('/api/images')
        .expect(404);
    });
  });
});
