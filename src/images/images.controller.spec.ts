import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import * as fs from 'node:fs';
import * as path from 'node:path';

import { ImagesController } from './images.controller';
import { ImagesService } from './images.service';
import { CreateImageDto } from './dto/create-image.dto';
import { 
  createMockFile, 
  createMockRequest, 
  createMockResponse, 
  clearAllMocks 
} from '../../test/utils/test-helpers';
import { 
  createMockImage, 
  createMockImages, 
  createMockCreateImageDto 
} from '../../test/utils/mock-data';

// Mock fs module
jest.mock('node:fs');
jest.mock('node:path');

describe('ImagesController', () => {
  let controller: ImagesController;
  let service: jest.Mocked<ImagesService>;

  const mockImagesService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    delete: jest.fn(),
    getRandom: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ImagesController],
      providers: [
        {
          provide: ImagesService,
          useValue: mockImagesService,
        },
      ],
    }).compile();

    controller = module.get<ImagesController>(ImagesController);
    service = module.get<ImagesService>(ImagesService) as jest.Mocked<ImagesService>;
  });

  afterEach(() => {
    clearAllMocks();
  });

  describe('create', () => {
    it('should create an image successfully', async () => {
      // Arrange
      const file = createMockFile();
      const createImageDto = createMockCreateImageDto();
      const mockImage = createMockImage();
      
      service.create.mockResolvedValue(mockImage);

      // Act
      const result = await controller.create(file, createImageDto);

      // Assert
      expect(service.create).toHaveBeenCalledWith(createImageDto, file);
      expect(result).toEqual(mockImage);
    });

    it('should throw HttpException when file is not provided', async () => {
      // Arrange
      const createImageDto = createMockCreateImageDto();

      // Act & Assert
      await expect(controller.create(undefined as any, createImageDto))
        .rejects.toThrow(new HttpException('File is required', HttpStatus.BAD_REQUEST));
      
      expect(service.create).not.toHaveBeenCalled();
    });

    it('should handle service errors', async () => {
      // Arrange
      const file = createMockFile();
      const createImageDto = createMockCreateImageDto();
      const error = new Error('Database error');
      
      service.create.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.create(file, createImageDto)).rejects.toThrow(error);
    });
  });

  describe('findAll', () => {
    it('should return all images', async () => {
      // Arrange
      const mockImages = createMockImages(3);
      service.findAll.mockResolvedValue(mockImages);

      // Act
      const result = await controller.findAll();

      // Assert
      expect(service.findAll).toHaveBeenCalled();
      expect(result).toEqual(mockImages);
    });

    it('should return empty array when no images exist', async () => {
      // Arrange
      service.findAll.mockResolvedValue([]);

      // Act
      const result = await controller.findAll();

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('next', () => {
    it('should return random images with default size', async () => {
      // Arrange
      const mockImages = createMockImages(1);
      const mockRequest = createMockRequest();
      
      service.getRandom.mockResolvedValue(mockImages);

      // Act
      const result = await controller.next(undefined, mockRequest as any);

      // Assert
      expect(service.getRandom).toHaveBeenCalledWith(1, []);
      expect(mockRequest.session.seenIds).toEqual([1]);
      expect(result).toEqual(mockImages);
    });

    it('should return random images with specified size', async () => {
      // Arrange
      const mockImages = createMockImages(3);
      const mockRequest = createMockRequest();
      
      service.getRandom.mockResolvedValue(mockImages);

      // Act
      const result = await controller.next('3', mockRequest);

      // Assert
      expect(service.getRandom).toHaveBeenCalledWith(3, []);
      expect(mockRequest.session.seenIds).toEqual([1, 2, 3]);
      expect(result).toEqual(mockImages);
    });

    it('should exclude previously seen images', async () => {
      // Arrange
      const mockImages = createMockImages(2);
      const mockRequest = createMockRequest({ seenIds: [1, 2] });
      
      service.getRandom.mockResolvedValue(mockImages);

      // Act
      const result = await controller.next('2', mockRequest);

      // Assert
      expect(service.getRandom).toHaveBeenCalledWith(2, [1, 2]);
      expect(mockRequest.session.seenIds).toEqual([1, 2, 1, 2]);
      expect(result).toEqual(mockImages);
    });

    it('should handle minimum size of 1', async () => {
      // Arrange
      const mockImages = createMockImages(1);
      const mockRequest = createMockRequest();
      
      service.getRandom.mockResolvedValue(mockImages);

      // Act
      const result = await controller.next('0', mockRequest);

      // Assert
      expect(service.getRandom).toHaveBeenCalledWith(1, []);
      expect(result).toEqual(mockImages);
    });

    it('should throw NotFoundException when no images available', async () => {
      // Arrange
      const mockRequest = createMockRequest();
      service.getRandom.mockResolvedValue([]);

      // Act & Assert
      await expect(controller.next('1', mockRequest))
        .rejects.toThrow(new NotFoundException('No more images available'));
    });

    it('should initialize seenIds if not present in session', async () => {
      // Arrange
      const mockImages = createMockImages(1);
      const mockRequest = { session: {} } as any;
      
      service.getRandom.mockResolvedValue(mockImages);

      // Act
      await controller.next('1', mockRequest);

      // Assert
      expect(mockRequest.session.seenIds).toBeDefined();
      expect(mockRequest.session.seenIds).toEqual([1]);
    });
  });

  describe('reset', () => {
    it('should reset seenIds in session', () => {
      // Arrange
      const mockRequest = createMockRequest({ seenIds: [1, 2, 3] });

      // Act
      controller.reset(mockRequest);

      // Assert
      expect(mockRequest.session.seenIds).toEqual([]);
    });

    it('should handle session without seenIds', () => {
      // Arrange
      const mockRequest = { session: {} } as any;

      // Act
      controller.reset(mockRequest);

      // Assert
      expect(mockRequest.session.seenIds).toEqual([]);
    });
  });

  describe('getFile', () => {
    const mockFs = fs as jest.Mocked<typeof fs>;
    const mockPath = path as jest.Mocked<typeof path>;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should serve file when it exists', async () => {
      // Arrange
      const filename = 'test-image.jpg';
      const mockResponse = createMockResponse();
      const filePath = '/path/to/file';
      
      mockPath.join.mockReturnValue(filePath);
      mockFs.existsSync.mockReturnValue(true);

      // Act
      await controller.getFile(filename, mockResponse);

      // Assert
      expect(mockPath.join).toHaveBeenCalled();
      expect(mockFs.existsSync).toHaveBeenCalledWith(filePath);
      expect(mockResponse.sendFile).toHaveBeenCalledWith(filename, { root: expect.any(String) });
    });

    it('should return 404 when file does not exist', async () => {
      // Arrange
      const filename = 'nonexistent.jpg';
      const mockResponse = createMockResponse();
      const filePath = '/path/to/nonexistent/file';
      
      mockPath.join.mockReturnValue(filePath);
      mockFs.existsSync.mockReturnValue(false);

      // Act
      await controller.getFile(filename, mockResponse);

      // Assert
      expect(mockFs.existsSync).toHaveBeenCalledWith(filePath);
      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.send).toHaveBeenCalledWith({ message: 'File not found' });
    });
  });

  describe('findOne', () => {
    it('should return an image by id', async () => {
      // Arrange
      const id = 1;
      const mockImage = createMockImage();
      
      service.findOne.mockResolvedValue(mockImage);

      // Act
      const result = await controller.findOne(id);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith(id.toString());
      expect(result).toEqual(mockImage);
    });

    it('should throw HttpException when image not found', async () => {
      // Arrange
      const id = 999;
      service.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(controller.findOne(id))
        .rejects.toThrow(new HttpException('Image not found', HttpStatus.NOT_FOUND));
    });
  });

  describe('delete', () => {
    it('should delete an image successfully', async () => {
      // Arrange
      const id = 1;
      service.delete.mockResolvedValue(undefined);

      // Act
      const result = await controller.delete(id);

      // Assert
      expect(service.delete).toHaveBeenCalledWith(id.toString());
      expect(result).toEqual({ deleted: true });
    });

    it('should handle service errors during deletion', async () => {
      // Arrange
      const id = 1;
      const error = new NotFoundException('Image not found');
      
      service.delete.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.delete(id)).rejects.toThrow(error);
    });
  });
});
