import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppController } from '../src/app.controller';
import { AppService } from '../src/app.service';

describe('AppController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [AppService],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.setGlobalPrefix('api');
    await app.init();
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('Root Endpoint', () => {
    it('/api (GET) - should return Hello World!', () => {
      return request(app.getHttpServer())
        .get('/api')
        .expect(200)
        .expect('Hello World!');
    });

    it('/api (GET) - should return string content type', () => {
      return request(app.getHttpServer())
        .get('/api')
        .expect(200)
        .expect('Content-Type', /text\/html/);
    });
  });

  describe('HTTP Methods', () => {
    it('should support GET method', () => {
      return request(app.getHttpServer())
        .get('/api')
        .expect(200);
    });

    it('should return 404 for unsupported methods on root', () => {
      return request(app.getHttpServer())
        .post('/api')
        .expect(404);
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for non-existent routes', () => {
      return request(app.getHttpServer())
        .get('/api/non-existent-route')
        .expect(404);
    });
  });

  describe('Content Validation', () => {
    it('should return exact expected content', () => {
      return request(app.getHttpServer())
        .get('/api')
        .expect(200)
        .expect((res) => {
          expect(res.text).toBe('Hello World!');
          expect(res.text.length).toBe(12);
        });
    });
  });
});
