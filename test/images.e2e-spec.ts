import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as request from 'supertest';
import * as path from 'path';
import * as fs from 'fs';

import { ImagesModule } from '../src/images/images.module';
import { Image } from '../src/images/entities/image.entity';
import { TEST_DB_CONFIG } from './utils/mock-data';

describe('Images (e2e)', () => {
  let app: INestApplication;
  let imageId: number;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          ...TEST_DB_CONFIG,
          entities: [Image],
        }),
        ImagesModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.setGlobalPrefix('api');
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/api/images (GET)', () => {
    it('should return empty array initially', () => {
      return request(app.getHttpServer())
        .get('/api/images')
        .expect(200)
        .expect([]);
    });
  });

  describe('/api/images (POST)', () => {
    it('should create an image with file upload', async () => {
      // Create a test file
      const testImagePath = path.join(__dirname, 'fixtures', 'test-image.jpg');
      const testImageBuffer = Buffer.from('fake image data');
      
      // Ensure test directory exists
      const testDir = path.dirname(testImagePath);
      if (!fs.existsSync(testDir)) {
        fs.mkdirSync(testDir, { recursive: true });
      }
      
      // Create test file
      fs.writeFileSync(testImagePath, testImageBuffer);

      const response = await request(app.getHttpServer())
        .post('/api/images')
        .attach('file', testImagePath)
        .field('name', 'Test Image')
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe('Test Image');
      expect(response.body.fileType).toBe('image/jpeg');
      expect(response.body).toHaveProperty('fileName');
      expect(response.body).toHaveProperty('filePath');
      expect(response.body).toHaveProperty('createdAt');
      expect(response.body).toHaveProperty('updatedAt');

      imageId = response.body.id;

      // Cleanup test file
      if (fs.existsSync(testImagePath)) {
        fs.unlinkSync(testImagePath);
      }
    });

    it('should create an image without name', async () => {
      const testImagePath = path.join(__dirname, 'fixtures', 'test-image-2.jpg');
      const testImageBuffer = Buffer.from('fake image data 2');
      
      const testDir = path.dirname(testImagePath);
      if (!fs.existsSync(testDir)) {
        fs.mkdirSync(testDir, { recursive: true });
      }
      
      fs.writeFileSync(testImagePath, testImageBuffer);

      const response = await request(app.getHttpServer())
        .post('/api/images')
        .attach('file', testImagePath)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBeUndefined();
      expect(response.body.fileType).toBe('image/jpeg');

      // Cleanup
      if (fs.existsSync(testImagePath)) {
        fs.unlinkSync(testImagePath);
      }
    });

    it('should return 400 when no file is provided', () => {
      return request(app.getHttpServer())
        .post('/api/images')
        .field('name', 'Test Image')
        .expect(400)
        .expect(res => {
          expect(res.body.message).toBe('File is required');
        });
    });
  });

  describe('/api/images (GET) after creation', () => {
    it('should return all images', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/images')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      const image = response.body.find((img: any) => img.id === imageId);
      expect(image).toBeDefined();
      expect(image.name).toBe('Test Image');
    });
  });

  describe('/api/images/:id (GET)', () => {
    it('should return a specific image', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/images/${imageId}`)
        .expect(200);

      expect(response.body.id).toBe(imageId);
      expect(response.body.name).toBe('Test Image');
      expect(response.body).toHaveProperty('fileName');
      expect(response.body).toHaveProperty('fileType');
      expect(response.body).toHaveProperty('filePath');
    });

    it('should return 404 for non-existent image', () => {
      return request(app.getHttpServer())
        .get('/api/images/999999')
        .expect(404)
        .expect(res => {
          expect(res.body.message).toBe('Image not found');
        });
    });

    it('should handle invalid id format', () => {
      return request(app.getHttpServer())
        .get('/api/images/invalid-id')
        .expect(400);
    });
  });

  describe('/api/images/next (GET)', () => {
    beforeEach(async () => {
      // Reset session before each test
      await request(app.getHttpServer())
        .get('/api/images/reset')
        .expect(200);
    });

    it('should return random images with default size', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/images/next')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBe(1);
      expect(response.body[0]).toHaveProperty('id');
      expect(response.body[0]).toHaveProperty('name');
    });

    it('should return random images with specified size', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/images/next?size=2')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeLessThanOrEqual(2);
    });

    it('should handle size parameter correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/images/next?size=0')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBe(1); // Should default to 1
    });

    it('should track seen images in session', async () => {
      const agent = request.agent(app.getHttpServer());
      
      // First request
      const response1 = await agent
        .get('/api/images/next')
        .expect(200);

      expect(response1.body.length).toBe(1);
      const firstImageId = response1.body[0].id;

      // Second request should potentially return different image
      // (though with only one image in test DB, it might be the same)
      const response2 = await agent
        .get('/api/images/next')
        .expect(200);

      expect(response2.body.length).toBeLessThanOrEqual(1);
    });
  });

  describe('/api/images/reset (GET)', () => {
    it('should reset seen images', () => {
      return request(app.getHttpServer())
        .get('/api/images/reset')
        .expect(200);
    });
  });

  describe('/api/images/file/:filename (GET)', () => {
    it('should return 404 for non-existent file', () => {
      return request(app.getHttpServer())
        .get('/api/images/file/non-existent.jpg')
        .expect(404)
        .expect(res => {
          expect(res.body.message).toBe('File not found');
        });
    });
  });

  describe('/api/images/:id (DELETE)', () => {
    it('should delete an image', async () => {
      const response = await request(app.getHttpServer())
        .delete(`/api/images/${imageId}`)
        .expect(200);

      expect(response.body.deleted).toBe(true);

      // Verify image is deleted
      await request(app.getHttpServer())
        .get(`/api/images/${imageId}`)
        .expect(404);
    });

    it('should return 404 when deleting non-existent image', () => {
      return request(app.getHttpServer())
        .delete('/api/images/999999')
        .expect(404);
    });

    it('should handle invalid id format for deletion', () => {
      return request(app.getHttpServer())
        .delete('/api/images/invalid-id')
        .expect(400);
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed requests gracefully', () => {
      return request(app.getHttpServer())
        .post('/api/images')
        .send('invalid json')
        .expect(400);
    });

    it('should handle unsupported HTTP methods', () => {
      return request(app.getHttpServer())
        .patch('/api/images')
        .expect(404);
    });
  });

  describe('Content Type Handling', () => {
    it('should handle different image types', async () => {
      const testImagePath = path.join(__dirname, 'fixtures', 'test-image.png');
      const testImageBuffer = Buffer.from('fake png data');
      
      const testDir = path.dirname(testImagePath);
      if (!fs.existsSync(testDir)) {
        fs.mkdirSync(testDir, { recursive: true });
      }
      
      fs.writeFileSync(testImagePath, testImageBuffer);

      const response = await request(app.getHttpServer())
        .post('/api/images')
        .attach('file', testImagePath)
        .field('name', 'PNG Test Image')
        .expect(201);

      expect(response.body.fileType).toBe('image/png');

      // Cleanup
      if (fs.existsSync(testImagePath)) {
        fs.unlinkSync(testImagePath);
      }

      // Delete the created image
      await request(app.getHttpServer())
        .delete(`/api/images/${response.body.id}`)
        .expect(200);
    });
  });
});
