import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [AppService],
    }).compile();

    appController = app.get<AppController>(AppController);
    appService = app.get<AppService>(AppService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Controller Initialization', () => {
    it('should be defined', () => {
      expect(appController).toBeDefined();
    });

    it('should be an instance of AppController', () => {
      expect(appController).toBeInstanceOf(AppController);
    });

    it('should have access to AppService', () => {
      expect(appService).toBeDefined();
      expect(appService).toBeInstanceOf(AppService);
    });

    it('should have getHello method', () => {
      expect(appController.getHello).toBeDefined();
      expect(typeof appController.getHello).toBe('function');
    });
  });

  describe('getHello', () => {
    it('should return "Hello World!"', () => {
      expect(appController.getHello()).toBe('Hello World!');
    });
  });
});
