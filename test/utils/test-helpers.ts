import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Image } from '../../src/images/entities/image.entity';

/**
 * Utilitaires pour les tests Jest
 */

/**
 * Crée un mock repository TypeORM
 */
export const createMockRepository = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  delete: jest.fn(),
  count: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    where: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getMany: jest.fn(),
  })),
});

/**
 * Crée un mock pour Express.Multer.File
 */
export const createMockFile = (overrides: Partial<Express.Multer.File> = {}): Express.Multer.File => ({
  fieldname: 'file',
  originalname: 'test-image.jpg',
  encoding: '7bit',
  mimetype: 'image/jpeg',
  size: 1024,
  destination: './uploads/images',
  filename: 'test-image-123.jpg',
  path: './uploads/images/test-image-123.jpg',
  buffer: Buffer.from('fake image data'),
  stream: null as any,
  ...overrides,
});

/**
 * Crée un mock pour Request avec session
 */
export const createMockRequest = (sessionData: any = {}) => ({
  session: {
    seenIds: [],
    ...sessionData,
  },
});

/**
 * Crée un mock pour Response
 */
export const createMockResponse = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.sendFile = jest.fn().mockReturnValue(res);
  return res;
};

/**
 * Crée un module de test avec les dépendances mockées
 */
export const createTestingModule = async (providers: any[] = [], imports: any[] = []) => {
  const module: TestingModule = await Test.createTestingModule({
    imports,
    providers: [
      ...providers,
      {
        provide: getRepositoryToken(Image),
        useValue: createMockRepository(),
      },
    ],
  }).compile();

  return module;
};

/**
 * Attend un délai spécifié (utile pour les tests asynchrones)
 */
export const delay = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Génère un ID aléatoire pour les tests
 */
export const generateTestId = (): number => Math.floor(Math.random() * 1000) + 1;

/**
 * Nettoie les mocks après chaque test
 */
export const clearAllMocks = () => {
  jest.clearAllMocks();
};
