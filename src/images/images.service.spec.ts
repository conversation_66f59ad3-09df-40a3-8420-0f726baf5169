import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, InternalServerErrorException } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';

import { ImagesService } from './images.service';
import { Image } from './entities/image.entity';
import { CreateImageDto } from './dto/create-image.dto';

// Mock fs module
jest.mock('fs', () => ({
  promises: {
    access: jest.fn(),
    unlink: jest.fn(),
  },
  constants: {
    F_OK: 0,
  },
}));

describe('ImagesService', () => {
  let service: ImagesService;
  let repository: jest.Mocked<Repository<Image>>;
  let mockRepository: ReturnType<typeof createMockRepository>;

  beforeEach(async () => {
    mockRepository = createMockRepository();
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImagesService,
        {
          provide: getRepositoryToken(Image),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<ImagesService>(ImagesService);
    repository = module.get<Repository<Image>>(getRepositoryToken(Image)) as jest.Mocked<Repository<Image>>;
  });

  afterEach(() => {
    clearAllMocks();
  });

  describe('create', () => {
    it('should create and save an image', async () => {
      // Arrange
      const createImageDto = createMockCreateImageDto();
      const file = createMockFile();
      const mockImage = createMockImage();
      
      mockRepository.create.mockReturnValue(mockImage);
      mockRepository.save.mockResolvedValue(mockImage);

      // Act
      const result = await service.create(createImageDto, file);

      // Assert
      expect(mockRepository.create).toHaveBeenCalledWith({
        name: createImageDto.name,
        fileName: file.filename,
        fileType: file.mimetype,
        filePath: path.join('uploads/images', file.filename),
      });
      expect(mockRepository.save).toHaveBeenCalledWith(mockImage);
      expect(result).toEqual(mockImage);
    });

    it('should create image with undefined name', async () => {
      // Arrange
      const createImageDto = createMockCreateImageDto({ name: undefined });
      const file = createMockFile();
      const mockImage = createMockImage({ name: undefined });
      
      mockRepository.create.mockReturnValue(mockImage);
      mockRepository.save.mockResolvedValue(mockImage);

      // Act
      const result = await service.create(createImageDto, file);

      // Assert
      expect(mockRepository.create).toHaveBeenCalledWith({
        name: undefined,
        fileName: file.filename,
        fileType: file.mimetype,
        filePath: path.join('uploads/images', file.filename),
      });
      expect(result).toEqual(mockImage);
    });
  });

  describe('findAll', () => {
    it('should return all images', async () => {
      // Arrange
      const mockImages = createMockImages(3);
      mockRepository.find.mockResolvedValue(mockImages);

      // Act
      const result = await service.findAll();

      // Assert
      expect(mockRepository.find).toHaveBeenCalled();
      expect(result).toEqual(mockImages);
    });

    it('should return empty array when no images exist', async () => {
      // Arrange
      mockRepository.find.mockResolvedValue([]);

      // Act
      const result = await service.findAll();

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('findOne', () => {
    it('should return an image by id', async () => {
      // Arrange
      const mockImage = createMockImage();
      const id = '1';
      mockRepository.findOne.mockResolvedValue(mockImage);

      // Act
      const result = await service.findOne(id);

      // Assert
      expect(mockRepository.findOne).toHaveBeenCalledWith({ 
        where: { id: parseInt(id) } 
      });
      expect(result).toEqual(mockImage);
    });

    it('should return null when image not found', async () => {
      // Arrange
      const id = '999';
      mockRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await service.findOne(id);

      // Assert
      expect(result).toBeNull();
    });

    it('should handle string id conversion', async () => {
      // Arrange
      const mockImage = createMockImage();
      const id = '123';
      mockRepository.findOne.mockResolvedValue(mockImage);

      // Act
      await service.findOne(id);

      // Assert
      expect(mockRepository.findOne).toHaveBeenCalledWith({ 
        where: { id: 123 } 
      });
    });
  });

  describe('delete', () => {
    const mockFsPromises = fs.promises as jest.Mocked<typeof fs.promises>;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should delete image and file successfully', async () => {
      // Arrange
      const mockImage = createMockImage();
      const id = '1';
      
      mockRepository.findOne.mockResolvedValue(mockImage);
      mockFsPromises.access.mockResolvedValue(undefined);
      mockFsPromises.unlink.mockResolvedValue(undefined);
      mockRepository.delete.mockResolvedValue({ affected: 1, raw: {} });

      // Act
      await service.delete(id);

      // Assert
      expect(mockRepository.findOne).toHaveBeenCalledWith({ 
        where: { id: parseInt(id, 10) } 
      });
      expect(mockFsPromises.access).toHaveBeenCalled();
      expect(mockFsPromises.unlink).toHaveBeenCalled();
      expect(mockRepository.delete).toHaveBeenCalledWith(id);
    });

    it('should throw NotFoundException when image not found', async () => {
      // Arrange
      const id = '999';
      mockRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.delete(id)).rejects.toThrow(NotFoundException);
      expect(mockRepository.findOne).toHaveBeenCalledWith({ 
        where: { id: parseInt(id, 10) } 
      });
    });

    it('should handle file not found gracefully', async () => {
      // Arrange
      const mockImage = createMockImage();
      const id = '1';
      
      mockRepository.findOne.mockResolvedValue(mockImage);
      mockFsPromises.access.mockRejectedValue({ code: 'ENOENT' });
      mockRepository.delete.mockResolvedValue({ affected: 1, raw: {} });

      // Act
      await service.delete(id);

      // Assert
      expect(mockRepository.delete).toHaveBeenCalledWith(id);
    });

    it('should throw InternalServerErrorException for file system errors', async () => {
      // Arrange
      const mockImage = createMockImage();
      const id = '1';
      
      mockRepository.findOne.mockResolvedValue(mockImage);
      mockFsPromises.access.mockResolvedValue(undefined);
      mockFsPromises.unlink.mockRejectedValue({ code: 'EPERM', message: 'Permission denied' });

      // Act & Assert
      await expect(service.delete(id)).rejects.toThrow(InternalServerErrorException);
    });

    it('should throw NotFoundException when database delete fails', async () => {
      // Arrange
      const mockImage = createMockImage();
      const id = '1';
      
      mockRepository.findOne.mockResolvedValue(mockImage);
      mockFsPromises.access.mockResolvedValue(undefined);
      mockFsPromises.unlink.mockResolvedValue(undefined);
      mockRepository.delete.mockResolvedValue({ affected: 0, raw: {} });

      // Act & Assert
      await expect(service.delete(id)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getRandom', () => {
    it('should return random images excluding specified IDs', async () => {
      // Arrange
      const mockImages = createMockImages(2);
      const take = 2;
      const excludeIds = [1, 2];
      
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockImages),
      };
      
      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      // Act
      const result = await service.getRandom(take, excludeIds);

      // Assert
      expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('image');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'image.id NOT IN (:...excludeIds)', 
        { excludeIds }
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('RANDOM()');
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(take);
      expect(result).toEqual(mockImages);
    });

    it('should return random images without exclusions when excludeIds is empty', async () => {
      // Arrange
      const mockImages = createMockImages(3);
      const take = 3;
      const excludeIds: number[] = [];
      
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockImages),
      };
      
      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      // Act
      const result = await service.getRandom(take, excludeIds);

      // Assert
      expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('image');
      expect(mockQueryBuilder.where).not.toHaveBeenCalled();
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('RANDOM()');
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(take);
      expect(result).toEqual(mockImages);
    });

    it('should return empty array when no images match criteria', async () => {
      // Arrange
      const take = 5;
      const excludeIds = [1, 2, 3];
      
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      };
      
      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      // Act
      const result = await service.getRandom(take, excludeIds);

      // Assert
      expect(result).toEqual([]);
    });
  });
});
