import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [AppService],
    }).compile();

    appController = app.get<AppController>(AppController);
    appService = app.get<AppService>(AppService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Controller Initialization', () => {
    it('should be defined', () => {
      expect(appController).toBeDefined();
    });

    it('should be an instance of AppController', () => {
      expect(appController).toBeInstanceOf(AppController);
    });

    it('should have access to AppService', () => {
      expect(appService).toBeDefined();
      expect(appService).toBeInstanceOf(AppService);
    });

    it('should have getHello method', () => {
      expect(appController.getHello).toBeDefined();
      expect(typeof appController.getHello).toBe('function');
    });
  });

  describe('getHello', () => {
    it('should return "Hello World!"', () => {
      // Act
      const result = appController.getHello();

      // Assert
      expect(result).toBe('Hello World!');
    });

    it('should call appService.getHello', () => {
      // Arrange
      const serviceSpy = jest.spyOn(appService, 'getHello');

      // Act
      appController.getHello();

      // Assert
      expect(serviceSpy).toHaveBeenCalled();
      expect(serviceSpy).toHaveBeenCalledTimes(1);
    });

    it('should return the same value as appService.getHello', () => {
      // Arrange
      const serviceResult = appService.getHello();

      // Act
      const controllerResult = appController.getHello();

      // Assert
      expect(controllerResult).toBe(serviceResult);
    });

    it('should return a string', () => {
      // Act
      const result = appController.getHello();

      // Assert
      expect(typeof result).toBe('string');
    });

    it('should not be null or undefined', () => {
      // Act
      const result = appController.getHello();

      // Assert
      expect(result).not.toBeNull();
      expect(result).not.toBeUndefined();
    });
  });

  describe('Controller with Mocked Service', () => {
    let mockAppService: jest.Mocked<AppService>;
    let controllerWithMock: AppController;

    beforeEach(async () => {
      const mockService = {
        getHello: jest.fn(),
      };

      const module: TestingModule = await Test.createTestingModule({
        controllers: [AppController],
        providers: [
          {
            provide: AppService,
            useValue: mockService,
          },
        ],
      }).compile();

      controllerWithMock = module.get<AppController>(AppController);
      mockAppService = module.get<AppService>(AppService) as jest.Mocked<AppService>;
    });

    it('should call mocked service method', () => {
      // Arrange
      mockAppService.getHello.mockReturnValue('Mocked Hello!');

      // Act
      const result = controllerWithMock.getHello();

      // Assert
      expect(mockAppService.getHello).toHaveBeenCalled();
      expect(result).toBe('Mocked Hello!');
    });

    it('should handle service returning different values', () => {
      // Arrange
      mockAppService.getHello.mockReturnValue('Custom Message');

      // Act
      const result = controllerWithMock.getHello();

      // Assert
      expect(result).toBe('Custom Message');
    });

    it('should handle service being called multiple times', () => {
      // Arrange
      mockAppService.getHello.mockReturnValue('Hello World!');

      // Act
      controllerWithMock.getHello();
      controllerWithMock.getHello();
      controllerWithMock.getHello();

      // Assert
      expect(mockAppService.getHello).toHaveBeenCalledTimes(3);
    });
  });

  describe('Error Handling', () => {
    it('should handle service errors gracefully', async () => {
      // Arrange
      const mockService = {
        getHello: jest.fn().mockImplementation(() => {
          throw new Error('Service error');
        }),
      };

      const module: TestingModule = await Test.createTestingModule({
        controllers: [AppController],
        providers: [
          {
            provide: AppService,
            useValue: mockService,
          },
        ],
      }).compile();

      const controller = module.get<AppController>(AppController);

      // Act & Assert
      expect(() => controller.getHello()).toThrow('Service error');
    });
  });

  describe('Integration Tests', () => {
    it('should work with real service integration', () => {
      // This test uses the real service, not a mock
      // Act
      const result = appController.getHello();

      // Assert
      expect(result).toBe('Hello World!');
    });

    it('should maintain consistency across multiple calls', () => {
      // Act
      const results = Array.from({ length: 5 }, () => appController.getHello());

      // Assert
      results.forEach(result => {
        expect(result).toBe('Hello World!');
      });
    });
  });
});
