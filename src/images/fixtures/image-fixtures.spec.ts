import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';
import * as https from 'https';
import * as http from 'http';

import { ImageFixtures } from './image-fixtures';
import { Image } from '../entities/image.entity';
import { createMockRepository, clearAllMocks } from '../../../test/utils/test-helpers';
import { createMockImage } from '../../../test/utils/mock-data';

// Mock modules
jest.mock('fs');
jest.mock('path');
jest.mock('https');
jest.mock('http');

describe('ImageFixtures', () => {
  let service: ImageFixtures;
  let repository: jest.Mocked<Repository<Image>>;
  let mockRepository: ReturnType<typeof createMockRepository>;

  const mockFs = fs as jest.Mocked<typeof fs>;
  const mockPath = path as jest.Mocked<typeof path>;
  const mockHttps = https as jest.Mocked<typeof https>;
  const mockHttp = http as jest.Mocked<typeof http>;

  beforeEach(async () => {
    mockRepository = createMockRepository();
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImageFixtures,
        {
          provide: getRepositoryToken(Image),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<ImageFixtures>(ImageFixtures);
    repository = module.get<Repository<Image>>(getRepositoryToken(Image)) as jest.Mocked<Repository<Image>>;
  });

  afterEach(() => {
    clearAllMocks();
  });

  describe('load', () => {
    beforeEach(() => {
      // Setup default mocks
      mockPath.join.mockReturnValue('/mock/path');
      mockFs.existsSync.mockReturnValue(true);
      mockFs.mkdirSync.mockReturnValue(undefined);
    });

    it('should skip loading when images already exist', async () => {
      // Arrange
      mockRepository.count.mockResolvedValue(5);
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      await service.load();

      // Assert
      expect(mockRepository.count).toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith('Skipping image fixtures: 5 images already exist');
      expect(mockRepository.create).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it('should create images directory if it does not exist', async () => {
      // Arrange
      mockRepository.count.mockResolvedValue(0);
      mockFs.existsSync.mockReturnValue(false);
      
      // Mock downloadImage to avoid actual HTTP calls
      jest.spyOn(service as any, 'downloadImage').mockResolvedValue('test-image.jpg');
      mockRepository.create.mockReturnValue(createMockImage());
      mockRepository.save.mockResolvedValue(createMockImage());

      // Act
      await service.load();

      // Assert
      expect(mockFs.existsSync).toHaveBeenCalled();
      expect(mockFs.mkdirSync).toHaveBeenCalledWith('/mock/path', { recursive: true });
    });

    it('should not create directory if it already exists', async () => {
      // Arrange
      mockRepository.count.mockResolvedValue(0);
      mockFs.existsSync.mockReturnValue(true);
      
      // Mock downloadImage to avoid actual HTTP calls
      jest.spyOn(service as any, 'downloadImage').mockResolvedValue('test-image.jpg');
      mockRepository.create.mockReturnValue(createMockImage());
      mockRepository.save.mockResolvedValue(createMockImage());

      // Act
      await service.load();

      // Assert
      expect(mockFs.existsSync).toHaveBeenCalled();
      expect(mockFs.mkdirSync).not.toHaveBeenCalled();
    });

    it('should download and save sample images', async () => {
      // Arrange
      mockRepository.count.mockResolvedValue(0);
      mockFs.existsSync.mockReturnValue(true);
      
      const downloadSpy = jest.spyOn(service as any, 'downloadImage')
        .mockResolvedValueOnce('image1.jpg')
        .mockResolvedValueOnce('image2.jpg')
        .mockResolvedValueOnce('image3.jpg');
      
      const mockImages = [
        createMockImage({ id: 1, name: 'Sample Image 1' }),
        createMockImage({ id: 2, name: 'Sample Image 2' }),
        createMockImage({ id: 3, name: 'Sample Image 3' }),
      ];
      
      mockRepository.create
        .mockReturnValueOnce(mockImages[0])
        .mockReturnValueOnce(mockImages[1])
        .mockReturnValueOnce(mockImages[2]);
      
      mockRepository.save
        .mockResolvedValueOnce(mockImages[0])
        .mockResolvedValueOnce(mockImages[1])
        .mockResolvedValueOnce(mockImages[2]);

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      await service.load();

      // Assert
      expect(downloadSpy).toHaveBeenCalledTimes(3);
      expect(mockRepository.create).toHaveBeenCalledTimes(3);
      expect(mockRepository.save).toHaveBeenCalledTimes(3);
      expect(consoleSpy).toHaveBeenCalledWith('Image fixtures loaded successfully');
      
      consoleSpy.mockRestore();
    });

    it('should handle download errors gracefully', async () => {
      // Arrange
      mockRepository.count.mockResolvedValue(0);
      mockFs.existsSync.mockReturnValue(true);
      
      const downloadSpy = jest.spyOn(service as any, 'downloadImage')
        .mockRejectedValueOnce(new Error('Download failed'))
        .mockResolvedValueOnce('image2.jpg')
        .mockResolvedValueOnce('image3.jpg');
      
      const mockImage = createMockImage();
      mockRepository.create.mockReturnValue(mockImage);
      mockRepository.save.mockResolvedValue(mockImage);

      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      await service.load();

      // Assert
      expect(downloadSpy).toHaveBeenCalledTimes(3);
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Failed to create fixture image Sample Image 1:',
        expect.any(Error)
      );
      expect(mockRepository.save).toHaveBeenCalledTimes(2); // Only 2 successful saves
      expect(consoleLogSpy).toHaveBeenCalledWith('Image fixtures loaded successfully');
      
      consoleErrorSpy.mockRestore();
      consoleLogSpy.mockRestore();
    });

    it('should handle repository errors gracefully', async () => {
      // Arrange
      mockRepository.count.mockResolvedValue(0);
      mockFs.existsSync.mockReturnValue(true);
      
      const downloadSpy = jest.spyOn(service as any, 'downloadImage')
        .mockResolvedValue('image.jpg');
      
      mockRepository.create.mockReturnValue(createMockImage());
      mockRepository.save.mockRejectedValue(new Error('Database error'));

      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();

      // Act
      await service.load();

      // Assert
      expect(consoleErrorSpy).toHaveBeenCalledTimes(3); // One for each failed save
      expect(consoleLogSpy).toHaveBeenCalledWith('Image fixtures loaded successfully');
      
      consoleErrorSpy.mockRestore();
      consoleLogSpy.mockRestore();
    });
  });

  describe('downloadImage', () => {
    let mockRequest: any;
    let mockResponse: any;

    beforeEach(() => {
      mockRequest = {
        on: jest.fn(),
        end: jest.fn(),
      };
      
      mockResponse = {
        statusCode: 200,
        on: jest.fn(),
        pipe: jest.fn(),
      };

      mockPath.join.mockReturnValue('/mock/path/image.jpg');
      mockPath.extname.mockReturnValue('.jpg');
    });

    it('should download image via HTTPS', async () => {
      // Arrange
      const url = 'https://example.com/image.jpg';
      const imagesDir = '/mock/images/dir';
      
      mockHttps.get.mockImplementation((url, callback: any) => {
        callback(mockResponse);
        return mockRequest;
      });

      const mockWriteStream = {
        on: jest.fn((event, callback) => {
          if (event === 'finish') {
            setTimeout(callback, 0);
          }
        }),
      };
      
      mockFs.createWriteStream.mockReturnValue(mockWriteStream as any);
      mockResponse.pipe.mockReturnValue(mockWriteStream);

      // Act
      const result = await (service as any).downloadImage(url, imagesDir);

      // Assert
      expect(mockHttps.get).toHaveBeenCalledWith(url, expect.any(Function));
      expect(mockFs.createWriteStream).toHaveBeenCalled();
      expect(mockResponse.pipe).toHaveBeenCalledWith(mockWriteStream);
      expect(result).toMatch(/\.jpg$/);
    });

    it('should download image via HTTP', async () => {
      // Arrange
      const url = 'http://example.com/image.jpg';
      const imagesDir = '/mock/images/dir';
      
      mockHttp.get.mockImplementation((url, callback: any) => {
        callback(mockResponse);
        return mockRequest;
      });

      const mockWriteStream = {
        on: jest.fn((event, callback) => {
          if (event === 'finish') {
            setTimeout(callback, 0);
          }
        }),
      };
      
      mockFs.createWriteStream.mockReturnValue(mockWriteStream as any);
      mockResponse.pipe.mockReturnValue(mockWriteStream);

      // Act
      const result = await (service as any).downloadImage(url, imagesDir);

      // Assert
      expect(mockHttp.get).toHaveBeenCalledWith(url, expect.any(Function));
      expect(result).toMatch(/\.jpg$/);
    });

    it('should handle HTTP errors', async () => {
      // Arrange
      const url = 'https://example.com/image.jpg';
      const imagesDir = '/mock/images/dir';
      const error = new Error('Network error');
      
      mockHttps.get.mockImplementation(() => {
        const req = mockRequest;
        setTimeout(() => req.on.mock.calls.find(call => call[0] === 'error')[1](error), 0);
        return req;
      });

      // Act & Assert
      await expect((service as any).downloadImage(url, imagesDir))
        .rejects.toThrow('Network error');
    });

    it('should handle non-200 status codes', async () => {
      // Arrange
      const url = 'https://example.com/image.jpg';
      const imagesDir = '/mock/images/dir';
      
      mockResponse.statusCode = 404;
      
      mockHttps.get.mockImplementation((url, callback: any) => {
        callback(mockResponse);
        return mockRequest;
      });

      // Act & Assert
      await expect((service as any).downloadImage(url, imagesDir))
        .rejects.toThrow('HTTP 404');
    });

    it('should handle write stream errors', async () => {
      // Arrange
      const url = 'https://example.com/image.jpg';
      const imagesDir = '/mock/images/dir';
      const writeError = new Error('Write error');
      
      mockHttps.get.mockImplementation((url, callback: any) => {
        callback(mockResponse);
        return mockRequest;
      });

      const mockWriteStream = {
        on: jest.fn((event, callback) => {
          if (event === 'error') {
            setTimeout(() => callback(writeError), 0);
          }
        }),
      };
      
      mockFs.createWriteStream.mockReturnValue(mockWriteStream as any);
      mockResponse.pipe.mockReturnValue(mockWriteStream);

      // Act & Assert
      await expect((service as any).downloadImage(url, imagesDir))
        .rejects.toThrow('Write error');
    });
  });
});
