import { ImageFixtures } from './image-fixtures';

describe('ImageFixtures', () => {
  let service: ImageFixtures;

  beforeEach(() => {
    service = new ImageFixtures(null as any);
  });

  describe('ImageFixtures Class', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should have load method', () => {
      expect(service.load).toBeDefined();
      expect(typeof service.load).toBe('function');
    });
  });
});
