import { Image } from '../../src/images/entities/image.entity';
import { CreateImageDto } from '../../src/images/dto/create-image.dto';

/**
 * Données de test mockées
 */

/**
 * Crée une image de test
 */
export const createMockImage = (overrides: Partial<Image> = {}): Image => {
  const image = new Image();
  image.id = 1;
  image.name = 'Test Image';
  image.fileName = 'test-image-123.jpg';
  image.fileType = 'image/jpeg';
  image.filePath = 'uploads/images/test-image-123.jpg';
  image.createdAt = new Date('2024-01-01T00:00:00.000Z');
  image.updatedAt = new Date('2024-01-01T00:00:00.000Z');
  
  return Object.assign(image, overrides);
};

/**
 * Crée plusieurs images de test
 */
export const createMockImages = (count: number = 3): Image[] => {
  return Array.from({ length: count }, (_, index) => 
    createMockImage({
      id: index + 1,
      name: `Test Image ${index + 1}`,
      fileName: `test-image-${index + 1}-123.jpg`,
      filePath: `uploads/images/test-image-${index + 1}-123.jpg`,
    })
  );
};

/**
 * Crée un DTO de création d'image de test
 */
export const createMockCreateImageDto = (overrides: Partial<CreateImageDto> = {}): CreateImageDto => ({
  name: 'Test Image',
  ...overrides,
});

/**
 * Données d'images pour les fixtures
 */
export const SAMPLE_IMAGES_DATA = [
  {
    name: 'Sample Image 1',
    url: 'https://picsum.photos/400/300?random=1',
    fileType: 'image/jpeg',
  },
  {
    name: 'Sample Image 2',
    url: 'https://picsum.photos/400/300?random=2',
    fileType: 'image/jpeg',
  },
  {
    name: 'Sample Image 3',
    url: 'https://picsum.photos/400/300?random=3',
    fileType: 'image/jpeg',
  },
];

/**
 * Configuration de base de données de test
 */
export const TEST_DB_CONFIG = {
  type: 'sqlite' as const,
  database: ':memory:',
  entities: [Image],
  synchronize: true,
  logging: false,
};

/**
 * Headers HTTP de test
 */
export const TEST_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

/**
 * Erreurs de test communes
 */
export const TEST_ERRORS = {
  NOT_FOUND: 'Image not found',
  FILE_REQUIRED: 'File is required',
  INTERNAL_ERROR: 'Internal server error',
  NO_MORE_IMAGES: 'No more images available',
};

/**
 * Chemins de fichiers de test
 */
export const TEST_PATHS = {
  UPLOADS: './test-uploads',
  IMAGES: './test-uploads/images',
  SAMPLE_IMAGE: './test/fixtures/sample-image.jpg',
};

/**
 * Tailles de fichiers de test
 */
export const TEST_FILE_SIZES = {
  SMALL: 1024, // 1KB
  MEDIUM: 1024 * 1024, // 1MB
  LARGE: 5 * 1024 * 1024, // 5MB
  TOO_LARGE: 10 * 1024 * 1024, // 10MB
};

/**
 * Types MIME de test
 */
export const TEST_MIME_TYPES = {
  JPEG: 'image/jpeg',
  PNG: 'image/png',
  GIF: 'image/gif',
  WEBP: 'image/webp',
  PDF: 'application/pdf', // Type non autorisé
  TEXT: 'text/plain', // Type non autorisé
};
